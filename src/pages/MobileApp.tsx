import { useState, useEffect } from "react";
import { DragDropContext, Droppable, DropResult } from "react-beautiful-dnd";
import { MobileScreen } from "@/components/mobile/MobileScreen";
import { DEVICE_MODELS, DeviceModel } from "@/components/mobile/DeviceSelector";
import { CategoryItem } from "@/components/mobile/CategoryItem";
import { FlashSale } from "@/components/mobile/FlashSale";
import { ProductCard } from "@/components/mobile/ProductCard";
import { BannerCard, BannerSwiper } from "@/components/mobile/BannerCard";
import { DraggableSection } from "@/components/mobile/DraggableSection";
import { BottomNavigation } from "@/components/mobile/BottomNavigation";
import { NewsList } from "@/components/mobile/NewsCard";
import { useToast } from "@/hooks/use-toast";
import appDataRaw from "./data.json";

// Type-safe app data with proper casting
const appData = appDataRaw as unknown as AppData;
import {
  Home,
  LayoutGrid,
  Search,
  User,
  ShoppingCart,
  FileText,
  ShoppingBag,
  Filter,
  MessageCircle,
  Settings,
  Gift,
  Package,
  Smartphone,
  Baby,
  Gamepad2,
  Palette,
  Apple,
  Car,
  Shirt,
  Milk,
  Monitor,
  Archive,
} from "lucide-react";

interface LayoutConfig {
  visible: boolean;
  settings?: any;
  data?: any;
  defaultData?: any[];
  loading?: boolean;
}

interface PageData {
  name: string;
  icon: string;
  href: string;
  layouts: {
    [layoutKey: string]: LayoutConfig;
  };
}

interface MenuConfig {
  visible: boolean;
  layouts: Array<{
    icon: string;
    label: string;
    href: string;
  }>;
}

interface AppData {
  page: {
    [pagePath: string]: PageData;
  };
  menuBottom: {
    [menuKey: string]: MenuConfig;
  };
  settings: {
    colorPrimary: string;
    colorSecondary: string;
    colorBackground: string;
    fontFamily: string;
  };
}

const MobileApp = () => {
  const { toast } = useToast();

  const [selectedPlatform, setSelectedPlatform] = useState<
    "iphone" | "android"
  >("iphone");
  const [selectedModel, setSelectedModel] = useState<string>("iphone-15-pro");
  const [sections, setSections] = useState<any[]>([]);
  const [bottomNavItems, setBottomNavItems] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState<string>("/home");

  // Access global settings
  const globalSettings = appData.settings;

  const getCurrentDevice = (): DeviceModel => {
    const models = DEVICE_MODELS[selectedPlatform];
    return models.find((model) => model.id === selectedModel) || models[0];
  };

  // Load data from JSON file based on current page using PageData interface
  const loadPageData = (pagePath: string) => {
    const pageData: PageData | undefined = appData.page[pagePath];
    const loadedSections: any[] = [];

    if (pageData && pageData.layouts) {
      // Type-safe iteration over layouts
      Object.entries(pageData.layouts).forEach(
        ([layoutKey, layoutConfig]: [string, LayoutConfig]) => {
          if (layoutConfig.visible) {
            // Sử dụng trực tiếp dữ liệu JSON
            loadedSections.push({
              id: layoutKey,
              ...layoutConfig,
            });
          }
        }
      );
    } else {
      console.warn(`⚠️ No page data found for: ${pagePath}`);
    }

    setSections(loadedSections);
    console.log(loadedSections);
  };

  useEffect(() => {
    loadPageData(currentPage);

    // Load bottom navigation dynamically with type safety
    const loadBottomNavigation = () => {
      // Find the first visible menu in menuBottom
      const menuBottomData = appData.menuBottom;
      if (!menuBottomData) {
        console.warn("⚠️ No menuBottom data found");
        return;
      }

      const availableMenus = Object.keys(menuBottomData);
      console.log("🔍 Available menu templates:", availableMenus);

      const activeMenuKey = availableMenus.find((menuKey) => {
        const menuConfig: MenuConfig = menuBottomData[menuKey];
        return menuConfig && menuConfig.visible;
      });

      if (activeMenuKey) {
        console.log("✅ Using menu template:", activeMenuKey);
        const menuConfig: MenuConfig = menuBottomData[activeMenuKey];

        const navItems = menuConfig.layouts.map((item, index: number) => ({
          id: item.href.replace("/", "") || `nav-${index}`,
          icon: getLucideIconIdentifier(item.icon),
          label: item.label,
          active: item.href === currentPage,
          href: item.href,
          menuTemplate: activeMenuKey, // Store which menu template is being used
        }));

        setBottomNavItems(navItems);
        console.log(
          `📱 Loaded ${navItems.length} navigation items from ${activeMenuKey}`
        );
      } else {
        console.warn("⚠️ No visible menu template found");
      }
    };

    loadBottomNavigation();

    // Log loaded templates for debugging
    console.log(`📱 Loading page: ${currentPage}`);
    console.log(`📊 Total sections loaded: ${sections.length}`);
    sections.forEach((section) => {
      console.log(
        `  - ${section.type} (${section.id}) - Template v${
          section.templateVersion || 1
        }`
      );
    });
  }, [currentPage]);

  // Helper function to convert icon names to string identifiers
  const getLucideIconIdentifier = (iconName: string): string => {
    const iconMap: { [key: string]: string } = {
      Home: "home",
      Category: "grid",
      Paper: "search", // FileText mapped to search for news
      User: "user",
      Search: "search",
      ShoppingCart: "cart",
      LayoutGrid: "grid",
      Cuboid: "shopping",
    };
    return iconMap[iconName] || "shopping";
  };

  // Handle navigation
  const handleNavigation = (href: string) => {
    setCurrentPage(href);
    // Update active state for bottom nav
    setBottomNavItems((prev) =>
      prev.map((item) => ({
        ...item,
        active: item.href === href,
      }))
    );
  };

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const items = Array.from(sections);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    setSections(items);
  };

  // Handler functions for the hover menu actions
  const handleEdit = (id: string) => {
    toast({
      title: "Edit Section",
      description: `Editing section: ${id}`,
    });
    console.log("Edit section:", id);
  };

  const handleCopy = (id: string) => {
    const sectionToCopy = sections.find((section) => section.id === id);
    if (sectionToCopy) {
      const newSection = {
        ...sectionToCopy,
        id: `${sectionToCopy.id}_copy_${Date.now()}`,
      };
      setSections((prev) => [...prev, newSection]);
      toast({
        title: "Section Copied",
        description: `Section ${id} has been copied`,
      });
    }
  };

  const handleDelete = (id: string) => {
    setSections((prev) => prev.filter((section) => section.id !== id));
    toast({
      title: "Section Deleted",
      description: `Section ${id} has been deleted`,
      variant: "destructive",
    });
  };

  // Dynamic section renderer based on template version
  const renderSection = (section: any, index: number) => {
    // Xác định type từ layoutKey
    const layoutKey = section.id;
    const templateMatch = layoutKey.match(/^([A-Za-z]+)(\d+)(_\d+)?$/);
    if (!templateMatch) return null;

    const [, templateType, templateVersion] = templateMatch;
    const type = templateType.toLowerCase();
    console.log(type);

    switch (type) {
      case "header":
        return renderHeaderTemplate(section, index, templateVersion);
      case "category":
        return renderCategoryTemplate(section, index, templateVersion);
      case "pagesearch":
        return renderSearchTemplate(section, index, templateVersion);
      case "pageprofile":
        return renderProfileTemplate(section, index, templateVersion);
      case "pagecart":
        return renderCartTemplate(section, index, templateVersion);
      case "pageproductdetail":
        return renderProductDetailTemplate(section, index, templateVersion);
      case "pagenewscategory":
        return renderNewsCategoryTemplate(section, index, templateVersion);
      case "flashsale":
        return renderFlashSaleTemplate(section, index, templateVersion);
      case "product":
        return renderProductsTemplate(section, index, templateVersion);
      case "banner":
        return renderBannersTemplate(section, index, templateVersion);
      case "news":
        return renderNewsTemplate(section, index, templateVersion);
      default:
        console.warn(`Unknown section type: ${type}`);
        return null;
    }
  };

  // Header template renderer with version support
  const renderHeaderTemplate = (
    section: any,
    index: number,
    version: number
  ) => {
    const settings = section.settings;

    return (
      <DraggableSection
        key={section.id}
        id={section.id}
        index={index}
        dragBackgroundColor={globalSettings.colorBackground}
        onEdit={handleEdit}
        onCopy={handleCopy}
        onDelete={handleDelete}
      >
        <div
          style={{
            background: settings?.background || "white",
            margin: settings?.margin || "0px",
            padding: settings?.padding || "8px",
            borderRadius: settings?.borderRadius || "0px",
          }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center justify-center gap-2">
              {settings?.visibleLogo && settings?.logo ? (
                <img
                  src={settings?.logo}
                  alt={settings?.title}
                  style={{
                    width: "40px",
                    objectFit: "contain",
                    maxWidth: "80px",
                  }}
                />
              ) : (
                <div className="w-full h-full bg-gray-100"></div>
              )}
              <div className="flex flex-col text-left">
                {settings?.visibleTitle && (
                  <span
                    style={{
                      color: settings?.colorTitle,
                      fontSize: `${settings?.fontSizeTitle || 16}px`,
                    }}
                  >
                    {settings?.title}
                  </span>
                )}
                {settings?.visibleSubTitle && (
                  <p
                    className="text-sm"
                    style={{
                      color: settings?.colorSubTitle,
                      fontSize: `${settings?.fontSizeSubTitle || 12}px`,
                    }}
                  >
                    {settings?.subTitle}
                  </p>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {settings?.visibleCartIcon && <ShoppingCart size={20} />}
              {settings?.visibleFilterIcon && <Filter size={20} />}
              {settings?.visibleMessageIcon && <MessageCircle size={20} />}
            </div>
          </div>
          {settings?.visibleSearchBar && (
            <div className="mt-2">
              <input
                type="text"
                placeholder={settings?.placeholderSearchBar}
                className="w-full px-3 py-2 border rounded-lg"
              />
            </div>
          )}
        </div>
      </DraggableSection>
    );
  };

  // Category template renderer with version support
  const renderCategoryTemplate = (
    section: any,
    index: number,
    version: number
  ) => {
    return (
      <DraggableSection
        key={section.id}
        id={section.id}
        index={index}
        dragBackgroundColor={globalSettings.colorBackground}
        onEdit={handleEdit}
        onCopy={handleCopy}
        onDelete={handleDelete}
      >
        <div
          className={`grid gap-2 ${
            section.settings?.col === 1
              ? "grid-cols-1"
              : section.settings?.col === 2
              ? "grid-cols-2"
              : section.settings?.col === 3
              ? "grid-cols-3"
              : section.settings?.col === 4
              ? "grid-cols-4"
              : section.settings?.col === 5
              ? "grid-cols-5"
              : section.settings?.col === 6
              ? "grid-cols-6"
              : section.settings?.col === 7
              ? "grid-cols-7"
              : section.settings?.col === 8
              ? "grid-cols-8"
              : section.settings?.col === 9
              ? "grid-cols-9"
              : section.settings?.col === 10
              ? "grid-cols-10"
              : section.settings?.col === 11
              ? "grid-cols-11"
              : section.settings?.col === 12
              ? "grid-cols-12"
              : "grid-cols-2" // default fallback
          }`}
          style={{
            margin: section.settings?.margin || "0px",
            padding: section.settings?.padding || "16px",
            background: section.settings?.background || "transparent",
            border: section.settings?.border || "none",
            borderRadius: section.settings?.borderRadius || "0px",
            boxShadow: section.settings?.boxShadow || "",
          }}
        >
          {section.data?.map((item: any, idx: number) => (
            <CategoryItem
              key={idx}
              title={item.name}
              image={item.image_url}
              onClick={() => item.url && handleNavigation(item.url)}
              cardStyle={section.settings?.cardStyle}
            />
          ))}
        </div>
      </DraggableSection>
    );
  };

  // Template renderer functions for different component types
  const renderSearchTemplate = (
    section: any,
    index: number,
    version: number
  ) => (
    <DraggableSection
      key={section.id}
      id={section.id}
      index={index}
      dragBackgroundColor={globalSettings.colorBackground}
      onEdit={handleEdit}
      onCopy={handleCopy}
      onDelete={handleDelete}
    >
      <div
        className="px-4 mb-4"
        style={{
          margin: section.settings?.margin || "0px",
          padding: section.settings?.padding || "16px",
        }}
      >
        <div
          className="p-4"
          style={{
            background: section.settings?.background || "white",
            border: section.settings?.border || "1px solid #e5e7eb",
            borderRadius: section.settings?.borderRadius || "8px",
            boxShadow: section.settings?.boxShadow || "",
          }}
        >
          <input
            type="text"
            placeholder="Tìm kiếm sản phẩm..."
            className="w-full px-3 py-2 border rounded-lg mb-4"
          />
          <div className="text-center text-gray-500">
            <p>Nhập từ khóa để tìm kiếm sản phẩm</p>
          </div>
        </div>
      </div>
    </DraggableSection>
  );

  const renderProfileTemplate = (
    section: any,
    index: number,
    version: number
  ) => (
    <DraggableSection
      key={section.id}
      id={section.id}
      index={index}
      dragBackgroundColor={globalSettings.colorBackground}
      onEdit={handleEdit}
      onCopy={handleCopy}
      onDelete={handleDelete}
    >
      <div
        className="px-4 mb-4"
        style={{
          margin: section.settings?.margin || "0px",
          padding: section.settings?.padding || "16px",
        }}
      >
        <div
          className="p-4"
          style={{
            background: section.settings?.background || "white",
            border: section.settings?.border || "1px solid #e5e7eb",
            borderRadius: section.settings?.borderRadius || "8px",
            boxShadow: section.settings?.boxShadow || "",
          }}
        >
          <div className="flex items-center mb-4">
            <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
              <User size={32} className="text-gray-600" />
            </div>
            <div className="ml-3">
              <h3 className="font-semibold">Người dùng</h3>
              <p className="text-sm text-gray-500">
                Chào mừng bạn đến với ứng dụng
              </p>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between py-2">
              <span>Đơn hàng của tôi</span>
              <Package size={20} className="text-gray-600" />
            </div>
            <div className="flex items-center justify-between py-2">
              <span>Ưu đãi của tôi</span>
              <Gift size={20} className="text-gray-600" />
            </div>
            <div className="flex items-center justify-between py-2">
              <span>Cài đặt</span>
              <Settings size={20} className="text-gray-600" />
            </div>
          </div>
        </div>
      </div>
    </DraggableSection>
  );

  const renderCartTemplate = (section: any, index: number, version: number) => (
    <DraggableSection
      key={section.id}
      id={section.id}
      index={index}
      dragBackgroundColor={globalSettings.colorBackground}
      onEdit={handleEdit}
      onCopy={handleCopy}
      onDelete={handleDelete}
    >
      <div
        className="px-4 mb-4"
        style={{
          margin: section.settings?.margin || "0px",
          padding: section.settings?.padding || "16px",
        }}
      >
        <div
          className="p-4"
          style={{
            background: section.settings?.background || "white",
            border: section.settings?.border || "1px solid #e5e7eb",
            borderRadius: section.settings?.borderRadius || "8px",
            boxShadow: section.settings?.boxShadow || "",
          }}
        >
          <div className="text-center text-gray-500">
            <ShoppingCart size={48} className="mx-auto text-gray-400" />
            <p className="mt-2">Giỏ hàng trống</p>
            <p className="text-sm">Hãy thêm sản phẩm vào giỏ hàng</p>
          </div>
        </div>
      </div>
    </DraggableSection>
  );

  const renderProductDetailTemplate = (
    section: any,
    index: number,
    version: number
  ) => {
    const product = section.data;
    return (
      <DraggableSection
        key={section.id}
        id={section.id}
        index={index}
        dragBackgroundColor={globalSettings.colorBackground}
        onEdit={handleEdit}
        onCopy={handleCopy}
        onDelete={handleDelete}
      >
        <div
          className="px-4 mb-4"
          style={{
            margin: section.settings?.margin || "0px",
            padding: section.settings?.padding || "16px",
          }}
        >
          <div
            className="p-4"
            style={{
              background: section.settings?.background || "white",
              border: section.settings?.border || "1px solid #e5e7eb",
              borderRadius: section.settings?.borderRadius || "8px",
              boxShadow: section.settings?.boxShadow || "",
            }}
          >
            <img
              src={product.image_url}
              alt={product.name}
              className="w-full h-48 object-cover rounded mb-3"
            />
            <h3 className="font-semibold text-lg mb-2">{product.name}</h3>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg font-bold text-red-500">
                {product.details?.sales_price?.toLocaleString()}đ
              </span>
              {product.details?.mrp && (
                <span className="text-sm text-gray-500 line-through">
                  {product.details?.mrp?.toLocaleString()}đ
                </span>
              )}
            </div>
            <p className="text-sm text-gray-600 mb-3">
              {product.category?.name}
            </p>
            <div className="flex items-center justify-between">
              <span className="text-sm">Đã bán: {product.total_sold}</span>
              <span className="text-sm">
                Còn lại: {product.details?.current_stock}
              </span>
            </div>
          </div>
        </div>
      </DraggableSection>
    );
  };

  const renderNewsCategoryTemplate = (
    section: any,
    index: number,
    version: number
  ) => (
    <DraggableSection
      key={section.id}
      id={section.id}
      index={index}
      dragBackgroundColor={globalSettings.colorBackground}
      onEdit={handleEdit}
      onCopy={handleCopy}
      onDelete={handleDelete}
    >
      <div
        className="px-4 mb-4"
        style={{
          margin: section.settings?.margin || "0px",
          padding: section.settings?.padding || "16px",
        }}
      >
        <div
          className="p-4"
          style={{
            background: section.settings?.background || "white",
            border: section.settings?.border || "1px solid #e5e7eb",
            borderRadius: section.settings?.borderRadius || "8px",
            boxShadow: section.settings?.boxShadow || "",
          }}
        >
          <h3 className="font-semibold mb-3">Tin tức</h3>
          <div className="text-center text-gray-500">
            <FileText size={48} className="mx-auto text-gray-400" />
            <p className="mt-2">Chưa có tin tức</p>
          </div>
        </div>
      </div>
    </DraggableSection>
  );

  const renderFlashSaleTemplate = (
    section: any,
    index: number,
    version: number
  ) => {
    // Use defaultData if data is empty and useDefaultData is true
    const flashSaleData =
      section.data && section.data.length > 0
        ? section.data
        : section.useDefaultData && section.defaultData
        ? section.defaultData
        : [];

    return (
      <DraggableSection
        key={section.id}
        id={section.id}
        index={index}
        dragBackgroundColor={globalSettings.colorBackground}
        onEdit={handleEdit}
        onCopy={handleCopy}
        onDelete={handleDelete}
      >
        <div
          style={{
            margin: section.settings?.margin || "0px",
            padding: section.settings?.padding || "16px",
            background: section.settings?.background || "transparent",
            border: section.settings?.border || "none",
            borderRadius: section.settings?.borderRadius || "0px",
            boxShadow: section.settings?.boxShadow || "",
          }}
        >
          {/* Flash Sale Header */}
          <div className="flex items-center justify-between mb-4">
            {section.settings?.title && (
              <h2
                className="font-semibold"
                style={{
                  color: section.settings?.titleStyle?.color || "#000",
                  fontSize: section.settings?.titleStyle?.fontSize || 16,
                }}
              >
                {section.settings.title}
              </h2>
            )}
            {section.settings?.subTitle && (
              <span
                className="text-sm"
                style={{
                  color: section.settings?.subTitleStyle?.color || "#666",
                  fontSize: section.settings?.subTitleStyle?.fontSize || 13,
                }}
              >
                {section.settings.subTitle}
              </span>
            )}
          </div>

          {/* Flash Sale Timer */}
          {section.settings?.visibleTimer && (
            <FlashSale
              className="mb-4"
              cardStyle={section.settings?.timerStyle}
            />
          )}

          {/* Flash Sale Products */}
          {flashSaleData && flashSaleData.length > 0 && (
            <div
              className="flex gap-3 overflow-x-auto pb-2"
              style={{
                margin: section.settings?.listStyle?.margin || "0px",
                padding: section.settings?.listStyle?.padding || "0px",
                background:
                  section.settings?.listStyle?.background || "transparent",
                border: section.settings?.listStyle?.border || "none",
                borderRadius:
                  section.settings?.listStyle?.borderRadius || "0px",
                boxShadow: section.settings?.listStyle?.boxShadow || "",
                gap: section.settings?.listStyle?.gap || 12,
              }}
            >
              {flashSaleData.map((product: any) => (
                <div
                  key={product.xid}
                  style={{
                    minWidth: section.settings?.cardStyle?.width || 155,
                    height: section.settings?.cardStyle?.height || 200,
                  }}
                >
                  <ProductCard
                    image={product.image_url}
                    title={product.name}
                    price={product.details?.sales_price}
                    originalPrice={product.details?.mrp}
                    discount={product.discount}
                    rating={product.rating}
                    sold={product.total_sold}
                    cardStyle={section.settings?.cardStyle}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      </DraggableSection>
    );
  };

  const renderProductsTemplate = (
    section: any,
    index: number,
    version: number
  ) => {
    // Use defaultData if data is empty and useDefaultData is true
    const productsData =
      section.data && section.data.length > 0
        ? section.data
        : section.useDefaultData && section.defaultData
        ? section.defaultData
        : [];

    return (
      <DraggableSection
        key={section.id}
        id={section.id}
        index={index}
        dragBackgroundColor={globalSettings.colorBackground}
        onEdit={handleEdit}
        onCopy={handleCopy}
        onDelete={handleDelete}
      >
        <div
          style={{
            margin: section.settings?.margin || "0px",
            padding: section.settings?.padding || "16px",
            background: section.settings?.background || "transparent",
            border: section.settings?.border || "none",
            borderRadius: section.settings?.borderRadius || "0px",
            boxShadow: section.settings?.boxShadow || "",
          }}
        >
          {/* Title */}
          {section.settings?.title && (
            <h2
              className="font-semibold mb-4"
              style={{
                color: section.settings?.titleStyle?.color || "#000",
                fontSize: section.settings?.titleStyle?.fontSize || 16,
              }}
            >
              {section.settings.title}
            </h2>
          )}

          {/* Products Grid */}
          <div
            className="grid grid-cols-2 gap-3"
            style={{
              margin: section.settings?.listStyle?.margin || "0px",
              padding: section.settings?.listStyle?.padding || "0px",
              background:
                section.settings?.listStyle?.background || "transparent",
              border: section.settings?.listStyle?.border || "none",
              borderRadius: section.settings?.listStyle?.borderRadius || "0px",
              boxShadow: section.settings?.listStyle?.boxShadow || "",
              gap: section.settings?.listStyle?.gap || 12,
            }}
          >
            {productsData?.map((product: any) => (
              <ProductCard
                key={product.xid}
                image={product.image_url}
                title={product.name}
                price={product.details?.sales_price}
                originalPrice={product.details?.mrp}
                discount={product.discount}
                rating={product.rating}
                sold={product.total_sold}
                cardStyle={section.settings?.cardStyle}
              />
            ))}
          </div>
        </div>
      </DraggableSection>
    );
  };

  const renderBannersTemplate = (
    section: any,
    index: number,
    version: number
  ) => (
    <DraggableSection
      key={section.id}
      id={section.id}
      index={index}
      dragBackgroundColor={globalSettings.colorBackground}
      onEdit={handleEdit}
      onCopy={handleCopy}
      onDelete={handleDelete}
    >
      <div
        style={{
          margin: section.settings?.margin || "0px",
          padding: section.settings?.padding || "16px",
          background: section.settings?.background || "transparent",
          border: section.settings?.border || "none",
          borderRadius: section.settings?.borderRadius || "0px",
          boxShadow: section.settings?.boxShadow || "",
        }}
      >
        <BannerSwiper
          banners={section.data || []}
          settings={section.settings}
          autoplay={section.settings?.autoplay !== false}
          autoplayDelay={section.settings?.autoplay || 3000}
          showPagination={section.settings?.animationPagination !== false}
          height={
            section.settings?.imageStyle?.height
              ? `${section.settings.imageStyle.height}px`
              : "164px"
          }
        />
      </div>
    </DraggableSection>
  );

  const renderNewsTemplate = (section: any, index: number, version: number) => {
    const newsData = section.useDefaultData
      ? section.defaultData
      : section.data;

    const handleNewsClick = (news: any) => {
      toast({
        title: "Đọc tin tức",
        description: `Đang mở: ${news.title}`,
      });
      console.log("News clicked:", news);
    };

    return (
      <DraggableSection
        key={section.id}
        id={section.id}
        index={index}
        dragBackgroundColor={globalSettings.colorBackground}
        onEdit={handleEdit}
        onCopy={handleCopy}
        onDelete={handleDelete}
      >
        <div
          style={{
            margin: section.settings?.margin || "0px",
            padding: section.settings?.padding || "16px",
            background: section.settings?.background || "rgba(255,255,255,1)",
            border: section.settings?.border || "0px solid rgba(0,0,0,0.1)",
            borderRadius: section.settings?.borderRadius || "0px",
            boxShadow: section.settings?.boxShadow || "",
          }}
        >
          <NewsList
            newsList={newsData || []}
            title={section.settings?.title || "Tin tức"}
            titleStyle={section.settings?.titleStyle}
            cardStyle={section.settings?.cardStyle}
            onNewsClick={handleNewsClick}
          />
        </div>
      </DraggableSection>
    );
  };

  return (
    <div className="min-h-screen bg-muted/30 ">
      <div className="max-w-6xl mx-auto">
        {/* <DeviceSelector
          selectedPlatform={selectedPlatform}
          selectedModel={selectedModel}
          onPlatformChange={handlePlatformChange}
          onModelChange={setSelectedModel}
        /> */}

        <div className="flex justify-center">
          <MobileScreen deviceModel={getCurrentDevice()}>
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="mobile-app">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="h-full overflow-y-auto pb-16 scrollbar-hide"
                    style={{
                      scrollbarWidth: "none",
                      msOverflowStyle: "none",
                    }}
                  >
                    {/* Scrollable Content */}
                    {sections.map((section, index) =>
                      renderSection(section, index)
                    )}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>

            {/* Bottom Navigation */}
            <BottomNavigation
              items={bottomNavItems}
              onItemClick={(id) => {
                const navItem = bottomNavItems.find((item) => item.id === id);
                if (navItem && navItem.href) {
                  handleNavigation(navItem.href);
                }
              }}
            />
          </MobileScreen>
        </div>
      </div>
    </div>
  );
};

export default MobileApp;
